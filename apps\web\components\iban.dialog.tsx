"use client";

import * as React from "react";
import { useState } from "react";
import { useForm, revalidateLogic } from "@tanstack/react-form";
import { z } from "zod";
import { Globe, Landmark } from "lucide-react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Button } from "@workspace/ui/components/button";
import { FeedbackDialog } from "./feedback.dialog";
import { Separator } from "@workspace/ui/components/separator";
import { Input } from "@workspace/ui/components/input";

// Turkish IBAN validation schema (24 digits only; checksum not enforced)
const ibanSchema = z.object({
  iban: z
    .string()
    .length(24, "IBAN 24 haneli olmalıdır")
    .regex(/^\d{24}$/, "Yalnızca rakam giriniz"),
});

export interface IbanDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSubmit?: (data: IbanFormData) => void;
  showCloseButton?: boolean;
}

export type IbanFormData = z.infer<typeof ibanSchema>;

// Helper function to format IBAN with dashes
function formatIbanWithDashes(value: string): string {
  // Remove all non-digits
  const digits = value.replace(/\D/g, "");

  // Format as: XX-XXXX-XXXX-XXXX-XXXX-XXXX-XX (2+4+4+4+4+4+2 = 24 digits)
  let formatted = "";
  for (let i = 0; i < digits.length && i < 24; i++) {
    if (i === 2 || i === 6 || i === 10 || i === 14 || i === 18 || i === 22) {
      formatted += "-";
    }
    formatted += digits[i];
  }

  return formatted;
}

// Helper function to remove dashes for validation
function removeIbanDashes(value: string): string {
  return value.replace(/-/g, "");
}

// Helper function to extract digits from full IBAN (including TR prefix)
function extractIbanDigits(value: string): string {
  // Remove all non-alphanumeric characters first
  const cleaned = value.replace(/[^A-Za-z0-9]/g, "");

  // If it starts with TR, remove it and return the digits
  if (cleaned.toUpperCase().startsWith("TR")) {
    return cleaned.slice(2).replace(/\D/g, "");
  }

  // Otherwise just return digits
  return cleaned.replace(/\D/g, "");
}

export function IbanDialog({
  open = false,
  onOpenChange,
  onSubmit,
  showCloseButton = true,
}: IbanDialogProps) {
  const [showFeedbackDialog, setShowFeedbackDialog] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm({
    defaultValues: {
      iban: "",
    },
    validationLogic: revalidateLogic({
      mode: "change",
      modeAfterSubmission: "change",
    }),
    validators: {
      onSubmit: ({ value }) => {
        // Clean the IBAN value before validation
        const cleanIban = removeIbanDashes(value.iban);
        console.log("Form validation - Original:", value.iban);
        console.log("Form validation - Cleaned:", cleanIban);
        try {
          ibanSchema.parse({ iban: cleanIban });
          console.log("Form validation - SUCCESS");
          return undefined; // No error
        } catch (error) {
          console.log("Form validation - ERROR:", error);
          throw error;
        }
      },
    },

    onSubmit: async ({ value }) => {
      console.log("Form onSubmit called with value:", value);
      setIsSubmitting(true);
      try {
        console.log("Starting submission delay...");
        await new Promise((resolve) => setTimeout(resolve, 1500));
        console.log("Submission delay completed");
        // Remove dashes before submitting
        const cleanValue = { iban: removeIbanDashes(value.iban) };
        console.log("Clean value:", cleanValue);
        onSubmit?.(cleanValue);
        console.log("Setting feedback dialog to true");
        setShowFeedbackDialog(true);
      } catch (error) {
        console.error("IBAN submission failed:", error);
      } finally {
        console.log("Setting isSubmitting to false");
        setIsSubmitting(false);
      }
    },
  });

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
        >
          <DialogContent
            className="sm:max-w-[600px]"
            showCloseButton={showCloseButton}
          >
            <DialogHeader>
              <DialogTitle>{"IBAN Ekleme"}</DialogTitle>
            </DialogHeader>

            {/* IBAN Card Container */}
            <div className="mt-1 w-full p-6">
              {/* Bank Icon Header */}
              <div className="max-w-lg mx-auto">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <Globe className="size-12 text-foreground" />
                    <span className="text-xl font-mono font-semibold text-foreground bg-muted px-4">
                      {"IBAN NO"}
                    </span>
                  </div>
                  <div className="text-xl text-foreground font-mono font-semibold bg-muted px-4">
                    {new Date()
                      .toLocaleDateString("tr-TR", {
                        year: "numeric",
                        month: "2-digit",
                        day: "2-digit",
                      })
                      .split(".")
                      .join("-")}
                  </div>
                </div>
                <Separator />
                {/* IBAN Display Section */}
                <div className="space-y-4 mt-8">
                  <div className="flex">
                    <div className="rounded-l-sm flex items-center shrink-0 pl-4 px-3 py-2 mt text-background text-shadow-none bg-foreground">
                      TR
                    </div>
                    <div className="relative w-full flex items-stretch overflow-clip">
                      <form.Field
                        name="iban"
                        validators={{
                          onChange: ({ value }) => {
                            const cleanIban = removeIbanDashes(value);
                            console.log("Field validation - Original:", value);
                            console.log(
                              "Field validation - Cleaned:",
                              cleanIban,
                            );
                            console.log(
                              "Field validation - Length:",
                              cleanIban.length,
                            );
                            try {
                              ibanSchema.parse({
                                iban: cleanIban,
                              });
                              console.log("Field validation - SUCCESS");
                              return undefined; // No error
                            } catch (error) {
                              console.log("Field validation - ERROR:", error);
                              return "Geçersiz IBAN";
                            }
                          },
                        }}
                      >
                        {(field) => (
                          <>
                            <div className="absolute inset-0 md:text-xl px-4 py-2 border-2 border-transparent tracking-widest font-mono pointer-events-none font-bold text-muted-foreground/50 text-nowrap whitespace-pre text-base sm:text-xl translate-y-0.5 text-shadow-none">
                              {(() => {
                                const placeholder =
                                  "__ ____ ____ ____ ____ ____ __";
                                const value = field.state.value || "";
                                let result = "";
                                let valueIndex = 0;

                                for (let i = 0; i < placeholder.length; i++) {
                                  if (
                                    placeholder[i] === "_" ||
                                    placeholder[i] === " "
                                  ) {
                                    if (valueIndex < value.length) {
                                      result += " ";
                                      valueIndex++;
                                    } else {
                                      result += placeholder[i];
                                    }
                                  } else {
                                    result += placeholder[i];
                                  }
                                }

                                return result;
                              })()}
                            </div>
                            <Input
                              className="md:text-xl pr-0 tracking-widest text-border font-mono bg-none rounded-r-sm border-3 border-foreground -outline-offset-6"
                              size={30}
                              pattern="[0-9\-]{30}"
                              maxLength={30}
                              inputMode="numeric"
                              autoComplete="off"
                              spellCheck={false}
                              value={field.state.value}
                              onChange={(e) => {
                                const formatted = formatIbanWithDashes(
                                  e.target.value,
                                );
                                field.handleChange(formatted);
                              }}
                              onPaste={(e) => {
                                e.preventDefault();
                                const pastedText =
                                  e.clipboardData.getData("text");
                                const digits = extractIbanDigits(pastedText);
                                const formatted = formatIbanWithDashes(digits);
                                field.handleChange(formatted);
                                // Trigger validation immediately so Save enables after paste
                                field.handleBlur();
                              }}
                              onBlur={field.handleBlur}
                            />
                            {field.state.meta.errors.length > 0 && (
                              <div className="absolute -bottom-6 left-0 text-sm text-destructive">
                                {String(field.state.meta.errors[0])}
                              </div>
                            )}
                          </>
                        )}
                      </form.Field>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="primary"
                disabled={isSubmitting}
                processing={isSubmitting}
                onClick={async () => {
                  const ibanValue = form.getFieldValue("iban");
                  const cleanIban = removeIbanDashes(ibanValue);

                  // Validate manually
                  try {
                    ibanSchema.parse({ iban: cleanIban });
                  } catch (error) {
                    console.log("Manual validation failed:", error);
                    return;
                  }

                  // Submit manually
                  console.log("Manual submission starting...");
                  setIsSubmitting(true);
                  try {
                    await new Promise((resolve) => setTimeout(resolve, 1500));
                    const cleanValue = { iban: cleanIban };
                    onSubmit?.(cleanValue);
                    setShowFeedbackDialog(true);
                    console.log("Manual submission completed");
                  } catch (error) {
                    console.error("Manual submission failed:", error);
                  } finally {
                    setIsSubmitting(false);
                  }
                }}
              >
                {"KAYDET"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </form>
        {/* Feedback Dialog (nested inside parent dialog) */}
        <FeedbackDialog
          open={showFeedbackDialog}
          onOpenChange={(open) => {
            setShowFeedbackDialog(open);
            if (!open) onOpenChange?.(false);
          }}
          message="IBAN Adresi Kaydedildi"
          content={["Çekim işlemlerinde bu adres kullanılacaktır."]}
          onButtonClick={() => {
            onOpenChange?.(false);
          }}
          showCloseButton={false}
        />
      </Dialog>
    </>
  );
}
